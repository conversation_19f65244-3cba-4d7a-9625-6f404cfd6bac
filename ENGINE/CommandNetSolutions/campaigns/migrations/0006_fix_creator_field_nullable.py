# Generated by Django 4.2.16 on 2025-05-26 13:29

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("campaigns", "0005_tag_group_many_to_many_fix"),
    ]

    operations = [
        migrations.AddField(
            model_name="taggroup",
            name="creator",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="taggroup",
            name="icon",
            field=models.CharField(
                blank=True,
                help_text="Icon name (e.g., 'tag', 'folder', etc.)",
                max_length=50,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="taggroup",
            name="parent",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="campaigns.taggroup",
            ),
        ),
        migrations.AddField(
            model_name="taggroup",
            name="priority",
            field=models.IntegerField(
                default=0, help_text="Higher priority groups appear first"
            ),
        ),
    ]

# Generated manually to fix WorkflowExecution foreign key relationship

from django.db import migrations, models
import django.db.models.deletion


def migrate_campaign_id_to_foreign_key(apps, schema_editor):
    """
    Migrate existing campaign_id CharField values to the new ForeignKey relationship.
    """
    WorkflowExecution = apps.get_model('campaigns', 'WorkflowExecution')
    Campaign = apps.get_model('campaigns', 'Campaign')
    
    # Get all workflow executions
    workflow_executions = WorkflowExecution.objects.all()
    
    for workflow in workflow_executions:
        # Try to find the campaign by ID
        try:
            campaign = Campaign.objects.get(id=workflow.campaign_id)
            # Set the new foreign key field
            workflow.campaign_new = campaign
            workflow.save()
        except Campaign.DoesNotExist:
            # If campaign doesn't exist, we'll delete this workflow execution
            print(f"Warning: Campaign {workflow.campaign_id} not found for workflow {workflow.id}. Deleting workflow.")
            workflow.delete()


def reverse_migrate_foreign_key_to_campaign_id(apps, schema_editor):
    """
    Reverse migration: copy foreign key values back to campaign_id CharField.
    """
    WorkflowExecution = apps.get_model('campaigns', 'WorkflowExecution')
    
    # Get all workflow executions
    workflow_executions = WorkflowExecution.objects.all()
    
    for workflow in workflow_executions:
        if workflow.campaign_new:
            workflow.campaign_id = str(workflow.campaign_new.id)
            workflow.save()


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0006_fix_creator_field_nullable'),
    ]

    operations = [
        # Step 1: Add the new foreign key field as nullable
        migrations.AddField(
            model_name='workflowexecution',
            name='campaign_new',
            field=models.ForeignKey(
                null=True,
                blank=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='campaigns.campaign',
                help_text="Campaign this workflow belongs to"
            ),
        ),
        
        # Step 2: Migrate data from campaign_id to campaign_new
        migrations.RunPython(
            migrate_campaign_id_to_foreign_key,
            reverse_migrate_foreign_key_to_campaign_id
        ),
        
        # Step 3: Remove the old campaign_id field
        migrations.RemoveField(
            model_name='workflowexecution',
            name='campaign_id',
        ),
        
        # Step 4: Rename campaign_new to campaign
        migrations.RenameField(
            model_name='workflowexecution',
            old_name='campaign_new',
            new_name='campaign',
        ),
        
        # Step 5: Make the campaign field non-nullable
        migrations.AlterField(
            model_name='workflowexecution',
            name='campaign',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to='campaigns.campaign',
                help_text="Campaign this workflow belongs to"
            ),
        ),
        
        # Step 6: Update indexes
        migrations.AlterModelOptions(
            name='workflowexecution',
            options={
                'verbose_name': 'Workflow Execution',
                'verbose_name_plural': 'Workflow Executions',
                'ordering': ['-start_time']
            },
        ),
        
        # Step 7: Add new indexes
        migrations.AddIndex(
            model_name='workflowexecution',
            index=models.Index(fields=['campaign', 'workflow_type'], name='campaigns_w_campaig_new_idx'),
        ),
    ]

"""
Models for tracking workflow execution and progress.
"""
from django.db import models
from django.utils import timezone
import uuid

class WorkflowExecution(models.Model):
    """
    Model to track PyFlow workflow execution.
    """
    WORKFLOW_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    WORKFLOW_TYPE_CHOICES = [
        ('collection', 'Account Collection'),
        ('analysis', 'Account Analysis'),
        ('tagging', 'Account Tagging'),
        ('follow', 'Follow'),
        ('like', 'Like'),
        ('comment', 'Comment'),
        ('dm', 'Direct Message'),
        ('cep', 'CEP'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    campaign = models.ForeignKey('Campaign', on_delete=models.CASCADE, help_text="Campaign this workflow belongs to")
    workflow_name = models.Char<PERSON>ield(max_length=255, help_text="Name of the PyFlow workflow file")
    workflow_path = models.CharField(max_length=512, help_text="Path to the PyFlow workflow file")
    workflow_type = models.CharField(max_length=20, choices=WORKFLOW_TYPE_CHOICES)
    status = models.CharField(max_length=20, choices=WORKFLOW_STATUS_CHOICES, default='pending')
    start_time = models.DateTimeField(auto_now_add=True)
    end_time = models.DateTimeField(null=True, blank=True)
    duration = models.FloatField(default=0.0, help_text="Duration in seconds")
    progress = models.FloatField(default=0.0, help_text="Progress percentage (0-100)")
    total_items = models.IntegerField(default=0, help_text="Total number of items to process")
    processed_items = models.IntegerField(default=0, help_text="Number of items processed")
    successful_items = models.IntegerField(default=0, help_text="Number of items processed successfully")
    failed_items = models.IntegerField(default=0, help_text="Number of items that failed processing")
    error_message = models.TextField(blank=True, null=True, help_text="Error message if workflow failed")
    log_file = models.CharField(max_length=512, blank=True, null=True, help_text="Path to the workflow log file")
    parameters = models.JSONField(default=dict, help_text="Parameters passed to the workflow")
    results = models.JSONField(default=dict, help_text="Results of the workflow execution")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.get_workflow_type_display()} for campaign {self.campaign.name if self.campaign else 'Unknown'} ({self.get_status_display()})"

    def update_progress(self, processed_items, successful_items=None, failed_items=None):
        """
        Update workflow progress.

        Args:
            processed_items (int): Number of items processed
            successful_items (int): Number of items processed successfully
            failed_items (int): Number of items that failed processing
        """
        self.processed_items = processed_items

        if successful_items is not None:
            self.successful_items = successful_items

        if failed_items is not None:
            self.failed_items = failed_items

        if self.total_items > 0:
            self.progress = (self.processed_items / self.total_items) * 100

        self.updated_at = timezone.now()
        self.save(update_fields=['processed_items', 'successful_items', 'failed_items', 'progress', 'updated_at'])

    def complete(self, results=None):
        """
        Mark workflow as completed.

        Args:
            results (dict): Results of the workflow execution
        """
        self.status = 'completed'
        self.end_time = timezone.now()
        self.duration = (self.end_time - self.start_time).total_seconds()
        self.progress = 100.0

        if results:
            self.results = results

        self.updated_at = timezone.now()
        self.save()

    def fail(self, error_message=None):
        """
        Mark workflow as failed.

        Args:
            error_message (str): Error message
        """
        self.status = 'failed'
        self.end_time = timezone.now()
        self.duration = (self.end_time - self.start_time).total_seconds()

        if error_message:
            self.error_message = error_message

        self.updated_at = timezone.now()
        self.save()

    def cancel(self):
        """
        Mark workflow as cancelled.
        """
        self.status = 'cancelled'
        self.end_time = timezone.now()
        self.duration = (self.end_time - self.start_time).total_seconds()
        self.updated_at = timezone.now()
        self.save()

    class Meta:
        db_table = 'campaigns_workflow_execution'
        verbose_name = "Workflow Execution"
        verbose_name_plural = "Workflow Executions"
        ordering = ['-start_time']
        indexes = [
            models.Index(fields=['campaign', 'workflow_type']),
            models.Index(fields=['status']),
            models.Index(fields=['start_time']),
        ]


class WorkflowProgressUpdate(models.Model):
    """
    Model to store workflow progress updates.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workflow_execution = models.ForeignKey(WorkflowExecution, on_delete=models.CASCADE, related_name='progress_updates')
    timestamp = models.DateTimeField(auto_now_add=True)
    processed_items = models.IntegerField(default=0)
    successful_items = models.IntegerField(default=0)
    failed_items = models.IntegerField(default=0)
    progress = models.FloatField(default=0.0)
    message = models.TextField(blank=True, null=True)
    details = models.JSONField(default=dict)

    def __str__(self):
        return f"Progress update for {self.workflow_execution} at {self.timestamp}"

    class Meta:
        db_table = 'campaigns_workflow_progress_update'
        verbose_name = "Workflow Progress Update"
        verbose_name_plural = "Workflow Progress Updates"
        ordering = ['-timestamp']

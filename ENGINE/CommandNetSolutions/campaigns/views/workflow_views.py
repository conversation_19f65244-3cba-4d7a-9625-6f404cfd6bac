"""
Views for managing campaign workflows.

This module provides views for creating and running workflows for campaigns.
"""
import json
import logging
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.shortcuts import get_object_or_404

from campaigns.models import Campaign
from campaigns.models.workflow import WorkflowExecution
from campaigns.services.workflow_service import WorkflowService

logger = logging.getLogger(__name__)

@csrf_exempt
@require_http_methods(["POST"])
def run_collection_workflow(request, campaign_id):
    """
    Run a collection workflow for a campaign.

    Args:
        request (HttpRequest): HTTP request
        campaign_id (str): Campaign ID

    Returns:
        JsonResponse: JSON response with workflow execution result
    """
    try:
        # Get campaign
        campaign = get_object_or_404(Campaign, id=campaign_id)

        # Parse request body
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            data = {}

        # Get options
        options = data.get('options', {})

        # Run workflow
        workflow_service = WorkflowService()
        result = workflow_service.run_collection_workflow(
            campaign_id=str(campaign.id),
            options=options
        )

        return JsonResponse(result)

    except Exception as e:
        logger.exception(f"Error running collection workflow: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def run_analysis_workflow(request, campaign_id):
    """
    Run an analysis workflow for a campaign.

    Args:
        request (HttpRequest): HTTP request
        campaign_id (str): Campaign ID

    Returns:
        JsonResponse: JSON response with workflow execution result
    """
    try:
        # Get campaign
        campaign = get_object_or_404(Campaign, id=campaign_id)

        # Parse request body
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            data = {}

        # Get analysis settings and options
        analysis_settings = data.get('analysis_settings', {})
        options = data.get('options', {})

        # Run workflow
        workflow_service = WorkflowService()
        result = workflow_service.run_analysis_workflow(
            campaign_id=str(campaign.id),
            analysis_settings=analysis_settings,
            options=options
        )

        return JsonResponse(result)

    except Exception as e:
        logger.exception(f"Error running analysis workflow: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def run_engagement_workflow(request, campaign_id):
    """
    Run an engagement workflow for a campaign.

    Args:
        request (HttpRequest): HTTP request
        campaign_id (str): Campaign ID

    Returns:
        JsonResponse: JSON response with workflow execution result
    """
    try:
        # Get campaign
        campaign = get_object_or_404(Campaign, id=campaign_id)

        # Parse request body
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            data = {}

        # Get workflow type, accounts, and options
        workflow_type = data.get('workflow_type')
        if not workflow_type:
            return JsonResponse({
                'success': False,
                'error': "Workflow type is required"
            }, status=400)

        accounts = data.get('accounts', [])
        options = data.get('options', {})

        # Run workflow
        workflow_service = WorkflowService()
        result = workflow_service.run_engagement_workflow(
            campaign_id=str(campaign.id),
            workflow_type=workflow_type,
            accounts=accounts,
            options=options
        )

        return JsonResponse(result)

    except Exception as e:
        logger.exception(f"Error running engagement workflow: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@require_http_methods(["GET"])
def get_workflow_status(request, workflow_execution_id):
    """
    Get the status of a workflow execution.

    Args:
        request (HttpRequest): HTTP request
        workflow_execution_id (str): Workflow execution ID

    Returns:
        JsonResponse: JSON response with workflow execution status
    """
    try:
        # Get workflow status
        workflow_service = WorkflowService()
        result = workflow_service.get_workflow_status(workflow_execution_id)

        return JsonResponse(result)

    except Exception as e:
        logger.exception(f"Error getting workflow status: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@require_http_methods(["GET"])
def list_campaign_workflows(request, campaign_id):
    """
    List workflows for a campaign.

    Args:
        request (HttpRequest): HTTP request
        campaign_id (str): Campaign ID

    Returns:
        JsonResponse: JSON response with campaign workflows
    """
    try:
        # Get campaign
        campaign = get_object_or_404(Campaign, id=campaign_id)

        # Get workflows
        workflows = WorkflowExecution.objects.filter(campaign=campaign).order_by('-start_time')

        # Serialize workflows
        workflows_data = []
        for workflow in workflows:
            workflows_data.append({
                'id': str(workflow.id),
                'workflow_name': workflow.workflow_name,
                'workflow_type': workflow.workflow_type,
                'status': workflow.status,
                'progress': workflow.progress,
                'start_time': workflow.start_time.isoformat() if workflow.start_time else None,
                'end_time': workflow.end_time.isoformat() if workflow.end_time else None,
                'duration': workflow.duration,
                'processed_items': workflow.processed_items,
                'total_items': workflow.total_items
            })

        return JsonResponse({
            'success': True,
            'workflows': workflows_data
        })

    except Exception as e:
        logger.exception(f"Error listing campaign workflows: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
